# Notification Cache Update Fix

## Problem Summary

The notification system had cache inconsistency issues when unflagging or declining notification items:

1. **Incomplete cache updates**: Only the `action` field was updated, but `isRead` status wasn't synchronized
2. **Items reappearing**: When pagination loaded more data, unflagged items appeared in the "seen" section
3. **Cache-server mismatch**: Server marked items as `isRead: true` during unflag/decline operations, but cache wasn't updated accordingly
4. **Improper filtering**: Cache update logic always returned `true` in filters, so items were never actually removed or properly updated

## Root Cause

When a user unflagged or declined a notification:
- Server automatically sets `isRead: true` (marking it as read)
- Server sets appropriate `action` field (`UNFLAG` or `DECLINED`)
- <PERSON><PERSON> only updated the `action` field, missing the `isRead` change
- <PERSON><PERSON> continued showing item in "unread" section until next refetch
- Pagination would load the item again in "seen" section, causing duplicates

## Solution Implemented

### 1. Fixed Cache Update Logic

**Before:**
```typescript
// Only updated action field
cache.writeFragment({
  id: itemRef.__ref,
  fragment: gql`
    fragment UpdateNotificationAction on AdminNotification {
      action
    }
  `,
  data: {
    action: AdminReportActionType.Unflag,
  },
});
```

**After:**
```typescript
// Updates both action and isRead fields
cache.writeFragment({
  id: itemRef.__ref,
  fragment: gql`
    fragment UpdateNotificationActionAndRead on AdminNotification {
      action
      isRead
    }
  `,
  data: {
    action: AdminReportActionType.Unflag,
    isRead: true, // Server marks as read when unflagged
  },
});
```

### 2. Improved Apollo Client Cache Configuration

Enhanced the `adminNotifications` cache merge policy to implement unique set behavior:

```typescript
adminNotifications: {
  keyArgs: false,
  merge(existing: any, incoming: any, { args }: any) {
    if (!existing) return incoming;

    const existingItems = existing.items || [];
    const incomingItems = incoming.items || [];

    // Create a unique set behavior where incoming duplicate items replace existing ones based on item ID
    const existingItemsMap = new Map();
    existingItems.forEach((item: any) => {
      const id = item.__ref?.split(':')[1] || item.id;
      if (id) {
        existingItemsMap.set(id, item);
      }
    });

    // Add or replace with incoming items
    incomingItems.forEach((item: any) => {
      const id = item.__ref?.split(':')[1] || item.id;
      if (id) {
        existingItemsMap.set(id, item);
      }
    });

    // Convert back to array, maintaining order
    const mergedItems = Array.from(existingItemsMap.values());

    return {
      ...incoming,
      items: mergedItems,
      total: incoming.total,
    };
  },
},
```

### 3. Updated Functions

#### `handleUnflagNotification`
- Now updates both `action: UNFLAG` and `isRead: true`
- Properly synchronizes cache with server state
- Prevents items from reappearing in wrong sections

#### `handleDeclineClubRequest`
- Now updates both `action: DECLINED` and `isRead: true`
- Maintains consistency between cache and server
- Ensures declined requests move to "seen" section immediately

### 4. Added Comprehensive Tests

Created test suite to verify:
- Cache updates include both `action` and `isRead` fields
- Loading states are managed correctly
- Success toasts are shown
- Error handling works properly

## Benefits

1. **Immediate UI Updates**: Items move from "unread" to "seen" sections instantly
2. **No Duplicates**: Pagination won't show unflagged items in wrong sections
3. **Cache Consistency**: Cache state matches server state after operations
4. **Better UX**: Users see immediate feedback without waiting for refetch
5. **Unique Set Behavior**: Incoming duplicate items replace existing ones based on ID

## Testing

Run the tests to verify the fix:

```bash
npm test src/components/notifications/hooks/__tests__/useNotificationActions.test.tsx
```

## Files Modified

1. `src/components/notifications/hooks/useNotificationActions.tsx`
   - Fixed `handleUnflagNotification` cache updates
   - Fixed `handleDeclineClubRequest` cache updates
   - Removed console.log statements

2. `src/providers/ApolloClient.tsx`
   - Enhanced `adminNotifications` merge policy
   - Implemented unique set behavior for cache updates

3. `src/components/notifications/hooks/__tests__/useNotificationActions.test.tsx` (new)
   - Comprehensive test suite for cache update logic

## Usage

The fix is automatically applied when users:
- Unflag a post or event notification
- Decline a club creation request

The cache will immediately reflect the changes, moving items to the appropriate sections without requiring a refetch.
