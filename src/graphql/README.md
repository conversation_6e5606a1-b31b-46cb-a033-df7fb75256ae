# GraphQL Fragments

This directory contains reusable GraphQL fragments organized by domain/entity type.

## Directory Structure

```
src/graphql/fragments/
├── index.ts              # Fragment exports and documentation
├── common.graphql        # Common fragments (pagination, messages, etc.)
├── user.graphql          # User-related fragments
├── image.graphql         # Image/file upload fragments
├── association.graphql   # Association fragments
├── club.graphql          # Club-related fragments
├── report.graphql        # Report-related fragments
└── notification.graphql  # Notification fragments
```

## Usage

### In GraphQL Operations

Import fragments in your `.graphql` files by referencing them with the spread operator:

```graphql
query Users($paginationArgs: PaginationArgs!) {
  users(paginationArgs: $paginationArgs) {
    items {
      ...DetailedUser
    }
    ...PaginationInfo
  }
}
```

### Fragment Composition

Fragments can reference other fragments:

```graphql
fragment UserWithAssociation on User {
  ...DetailedUser
  association {
    ...BasicAssociation
  }
}
```

## Available Fragments

### Common Fragments (`common.graphql`)

- `PaginationInfo` - Standard pagination fields (total, page, limit)
- `MessageResponse` - Basic message response
- `ContactInfo` - Contact information fields
- `AddressInfo` - Address fields

### User Fragments (`user.graphql`)

- `BasicUser` - Minimal user fields (id, name, email, role)
- `DetailedUser` - Common user fields including phone, dob, etc.
- `UserWithAssociation` - User with association information
- `UserWithContact` - User with contact information
- `FullUser` - Complete user information

### Image Fragments (`image.graphql`)

- `BasicImage` - Minimal image fields (id, filename, url)
- `DetailedImage` - Complete image information
- `UploadFileWithPresignedUrl` - Upload response with presigned URL

### Association Fragments (`association.graphql`)

- `BasicAssociation` - Basic association fields
- `AssociationWithClubInfo` - Association with club usage info
- `DetailedAssociation` - Admin view association fields
- `FullAssociation` - Complete association information

### Club Fragments (`club.graphql`)

- `BasicClubProfile` - Minimal club profile
- `ClubProfileWithImage` - Club profile with image
- `ClubProfileWithUser` - Club profile with user details
- `BasicClubTemplate` - Basic club template
- `DetailedClubTemplate` - Complete club template
- `ClubWithTemplate` - Club with template information
- `ClubMemberInfo` - Club member details
- `BasicClubPost` - Basic club post
- `ClubPostWithProfile` - Club post with profile
- `BasicClubEvent` - Basic club event
- `ClubEventWithProfile` - Club event with profile
- `ClubRequestInfo` - Club request information

### Report Fragments (`report.graphql`)

- `BasicReportCategory` - Report category fields
- `DetailedReportCategory` - Complete report category
- `BasicReport` - Basic report information
- `ReportWithCategory` - Report with category
- `ReportWithReporter` - Report with reporter info
- `ClubPostReport` - Report for club posts
- `ClubEventReport` - Report for club events

### Notification Fragments (`notification.graphql`)

- `BasicAdminNotification` - Basic notification fields
- `AdminNotificationPayload` - Notification payload
- `AdminNotificationWithClubPost` - Notification with club post
- `AdminNotificationWithClubEvent` - Notification with club event
- `AdminNotificationWithClubRequest` - Notification with club request
- `CompleteAdminNotification` - Complete notification information

## Best Practices

1. **Use appropriate fragment size**: Choose the most specific fragment that meets your needs
2. **Compose fragments**: Build complex fragments from simpler ones
3. **Consistent naming**: Follow the established naming patterns
4. **Document changes**: Update this README when adding new fragments
5. **Test thoroughly**: Ensure fragments work correctly with code generation

## Migration Guide

When migrating existing queries to use fragments:

1. Identify repeated field patterns in your query
2. Find the appropriate fragment or create a new one
3. Replace the field list with the fragment reference
4. Test the query to ensure it still works
5. Run code generation to update TypeScript types

## Code Generation

Fragments are automatically included in the GraphQL Code Generator output. The generated TypeScript types will include proper fragment types that can be used in your React components.
