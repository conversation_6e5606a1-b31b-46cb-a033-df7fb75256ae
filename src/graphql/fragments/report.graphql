# Report-related fragments

# Basic report category information
fragment BasicReportCategory on ReportCategory {
  id
  code
  title
  description
}

# Detailed report category information
fragment DetailedReportCategory on ReportCategory {
  id
  code
  title
  description
  ordering
  createdAt
  updatedAt
}

# Basic report information
fragment BasicReport on ClubReport {
  id
  status
  details
  createdAt
}

# Report with category
fragment ReportWithCategory on ClubReport {
  id
  status
  details
  createdAt
  category {
    ...DetailedReportCategory
  }
}

# Report with reporter information
fragment ReportWithReporter on ClubReport {
  id
  status
  details
  createdAt
  category {
    ...DetailedReportCategory
  }
  reporter {
    id
    displayName
    createdAt
    updatedAt
  }
}

# Club post report (for posts)
fragment ClubPostReport on ClubReport {
  id
  status
  details
  createdAt
  category {
    ...DetailedReportCategory
  }
  reporter {
    id
    displayName
    createdAt
    updatedAt
  }
  clubPost {
    id
    clubId
    content
    isPinned
    reactionCount
    createdAt
    updatedAt
    clubProfile {
      ...BasicClubProfile
    }
  }
}

# Club event report (for events)
fragment ClubEventReport on ClubReport {
  id
  status
  details
  createdAt
  category {
    ...BasicReportCategory
  }
  clubEvent {
    id
    name
    startTime
    endTime
    location
    description
    reactionCount
    isNew
    hasReacted
    createdAt
    updatedAt
  }
}
