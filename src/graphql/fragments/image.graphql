# Image and file upload related fragments

# Basic image information (minimal fields for display)
fragment BasicImage on UploadFile {
  id
  filename
  url
}

# Detailed image information (all fields)
fragment DetailedImage on UploadFile {
  id
  filename
  key
  mimeType
  size
  status
  createdAt
  updatedAt
  url
}

# Upload file with presigned URL information
fragment UploadFileWithPresignedUrl on UploadUrlResponse {
  uploadFile {
    ...DetailedImage
  }
  presignedUrl {
    url
    fields {
      bucket
      key
      Policy
      X_Amz_Algorithm
      X_Amz_Credential
      X_Amz_Date
      X_Amz_Signature
    }
  }
}
