# Club-related fragments

# Basic club profile information
fragment BasicClubProfile on ClubProfile {
  id
  displayName
}

# Club profile with image
fragment ClubProfileWithImage on ClubProfile {
  id
  displayName
  img {
    ...BasicImage
  }
}

# Club profile with detailed image
fragment ClubProfileWithDetailedImage on ClubProfile {
  id
  displayName
  img {
    ...DetailedImage
  }
}

# Club profile with user information (for admin views)
fragment AdminClubProfileWithUser on AdminClubProfile {
  id
  displayName
  img {
    ...BasicImage
  }
  user {
    ...FullUser
  }
  createdAt
  updatedAt
}

# Basic club template information
fragment BasicClubTemplate on ClubTemplate {
  id
  name
  description
  about
  category
}

# Club template with image
fragment ClubTemplateWithImage on ClubTemplate {
  id
  name
  description
  about
  category
  newPost
  img {
    ...BasicImage
  }
}

# Detailed club template information
fragment DetailedClubTemplate on ClubTemplate {
  id
  name
  description
  about
  category
  newPost
  hasJoined
  memberCount
  img {
    ...DetailedImage
  }
  updatedAt
}

# Admin club information
fragment AdminClubInfo on AdminClub {
  id
  activatedAt
  memberCount
}

# Admin club with template information
fragment AdminClubWithTemplate on AdminClub {
  id
  activatedAt
  lastActivity
  memberCount
  clubTemplate {
    ...ClubTemplateWithImage
  }
}

# Club membership information
fragment ClubMembershipInfo on ClubMembership {
  id
  status
  joinedAt
  deletedAt
  clubProfile {
    ...ClubProfileWithImage
  }
}

# Admin club post basic information
fragment AdminClubPostBasic on AdminClubPost {
  id
  content
  isPinned
  reactionCount
  createdAt
  updatedAt
  deletedAt
}

# Admin club post with profile
fragment AdminClubPostWithProfile on AdminClubPost {
  id
  content
  isPinned
  reactionCount
  createdAt
  updatedAt
  deletedAt
  clubProfile {
    ...AdminClubProfileWithUser
  }
}

# Admin club event basic information
fragment AdminClubEventBasic on AdminClubEvent {
  id
  name
  startTime
  endTime
  location
  description
  reactionCount
  createdAt
  updatedAt
}

# Admin club event with profile
fragment AdminClubEventWithProfile on AdminClubEvent {
  id
  name
  startTime
  endTime
  location
  description
  reactionCount
  createdAt
  updatedAt
  clubProfile {
    ...AdminClubProfileWithUser
  }
}

# Club request information
fragment ClubRequestInfo on ClubRequest {
  id
  clubName
  clubDescription
  category
  clubAbout
  status
  createdAt
  clubProfile {
    ...ClubProfileWithImage
  }
  user {
    ...BasicUser
  }
}
