# Common fragments used across multiple queries

# Pagination information fragment - works with all paginated responses
fragment PaginationInfo on PaginatedAdminAssociationResponse {
  total
  page
  limit
}

# Pagination info for user responses
fragment UserPaginationInfo on PaginatedUserResponse {
  total
  page
  limit
}

# Pagination info for club template responses
fragment ClubTemplatePaginationInfo on PaginatedClubTemplateResponse {
  total
  page
  limit
}

# Pagination info for admin notification responses
fragment AdminNotificationPaginationInfo on PaginatedAdminNotificationsResponse {
  total
  page
  limit
}

fragment ClubMembersPaginationInfo on PaginatedClubMembersResponse {
  total
  page
  limit
}

fragment AdminClubPostsPaginationInfo on PaginatedAdminClubPostsResponse {
  total
  page
  limit
}

fragment AdminClubEventsPaginationInfo on PaginatedAdminClubEventsResponse {
  total
  page
  limit
}

fragment AdminClubMembersPaginationInfo on PaginatedAdminClubMembersResponse {
  total
  page
  limit
}

fragment UsersCreateEventsPaginationInfo on PaginatedUsersCreateEventsResponse {
  total
  page
  limit
}

fragment UsersCreatePostsPaginationInfo on PaginatedUsersCreatePostResponse {
  total
  page
  limit
}

# Basic message response fragment
fragment MessageResponse on Message {
  message
}

# Contact information fragment
fragment ContactInfo on Contact {
  id
  salesforceId
}

# Address information fragment
fragment AddressInfo on Address {
  id
  street
  city
  state
  zipCode
  isActive
  isPrimaryUnit
  source
  createdAt
  updatedAt
  isSameAsUnitAddress
}
