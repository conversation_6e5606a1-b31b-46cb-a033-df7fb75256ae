/**
 * GraphQL Fragments Index
 *
 * This file exports all GraphQL fragments for easy importing.
 * Fragments are organized by domain/entity type.
 */

// Re-export all fragments (they will be automatically included by codegen)
// This file serves as documentation and can be used for programmatic access if needed

export const FRAGMENT_FILES = [
  'common.graphql',
  'user.graphql',
  'image.graphql',
  'association.graphql',
  'club.graphql',
  'report.graphql',
  'notification.graphql',
] as const;

export const COMMON_FRAGMENTS = [
  'PaginationInfo',
  'MessageResponse',
  'ContactInfo',
  'AddressInfo',
] as const;

export const USER_FRAGMENTS = [
  'BasicUser',
  'DetailedUser',
  'UserWithAssociation',
  'UserWithContact',
  'FullUser',
] as const;

export const IMAGE_FRAGMENTS = [
  'BasicImage',
  'DetailedImage',
  'UploadFileWithPresignedUrl',
] as const;

export const ASSOCIATION_FRAGMENTS = [
  'BasicAssociation',
  'AssociationWithClubInfo',
  'DetailedAssociation',
  'FullAssociation',
] as const;

export const CLUB_FRAGMENTS = [
  'BasicClubProfile',
  'ClubProfileWithImage',
  'ClubProfileWithDetailedImage',
  'ClubProfileWithUser',
  'BasicClubTemplate',
  'ClubTemplateWithImage',
  'DetailedClubTemplate',
  'BasicClub',
  'ClubWithTemplate',
  'ClubMemberInfo',
  'BasicClubPost',
  'ClubPostWithProfile',
  'BasicClubEvent',
  'ClubEventWithProfile',
  'ClubRequestInfo',
] as const;

export const REPORT_FRAGMENTS = [
  'BasicReportCategory',
  'DetailedReportCategory',
  'BasicReport',
  'ReportWithCategory',
  'ReportWithReporter',
  'ClubPostReport',
  'ClubEventReport',
] as const;

export const NOTIFICATION_FRAGMENTS = [
  'BasicAdminNotification',
  'AdminNotificationPayload',
  'AdminNotificationWithClubPost',
  'AdminNotificationWithClubEvent',
  'AdminNotificationWithClubRequest',
  'CompleteAdminNotification',
] as const;
