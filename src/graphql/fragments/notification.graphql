# Notification-related fragments

# Basic admin notification information
fragment BasicAdminNotification on AdminNotification {
  id
  type
  isRead
  action
  createdAt
  updatedAt
  reportedAt
}

# Admin notification payload
fragment AdminNotificationPayload on AdminNotificationPayload {
  postId
  eventId
  clubRequestId
}

# Admin notification with club post
fragment AdminNotificationWithClubPost on AdminNotification {
  id
  type
  isRead
  action
  createdAt
  updatedAt
  reportedAt
  payload {
    ...AdminNotificationPayload
  }
  clubPost {
    ...AdminClubPostWithProfile
    reactions {
      id
      postId
      clubProfileId
      createdAt
    }
    reports {
      ...ClubPostReport
    }
  }
}

# Admin notification with club event
fragment AdminNotificationWithClubEvent on AdminNotification {
  id
  type
  isRead
  action
  createdAt
  updatedAt
  reportedAt
  payload {
    ...AdminNotificationPayload
  }
  clubEvent {
    ...AdminClubEventWithProfile
    reports {
      ...ReportWithCategory
    }
  }
}

# Admin notification with club request
fragment AdminNotificationWithClubRequest on AdminNotification {
  id
  type
  isRead
  action
  createdAt
  updatedAt
  reportedAt
  payload {
    ...AdminNotificationPayload
  }
  clubRequest {
    ...ClubRequestInfo
  }
}

# Complete admin notification (all possible fields)
fragment CompleteAdminNotification on AdminNotification {
  id
  type
  isRead
  action
  createdAt
  updatedAt
  reportedAt
  payload {
    ...AdminNotificationPayload
  }
  clubPost {
    ...AdminClubPostWithProfile
    reactions {
      id
      postId
      clubProfileId
      createdAt
    }
    reports {
      ...ClubPostReport
    }
  }
  clubEvent {
    ...AdminClubEventWithProfile
    reports {
      ...ReportWithCategory
    }
  }
  clubRequest {
    ...ClubRequestInfo
  }
}
