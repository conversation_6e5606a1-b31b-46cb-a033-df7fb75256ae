# User-related fragments

# Basic user information (minimal fields)
fragment BasicUser on User {
  id
  firstName
  lastName
  email
  role
}

# Detailed user information (commonly used fields)
fragment DetailedUser on User {
  id
  firstName
  lastName
  email
  phone
  dob
  birthdayMonth
  birthdayDay
  dateJoined
  role
  isDeleted
  canUseClubs
}

# User with association information
fragment UserWithAssociation on User {
  id
  firstName
  lastName
  email
  phone
  dob
  birthdayMonth
  birthdayDay
  dateJoined
  role
  isDeleted
  canUseClubs
  association {
    ...BasicAssociation
  }
}

# User with contact information
fragment UserWithContact on User {
  id
  firstName
  lastName
  email
  phone
  dob
  dateJoined
  role
  isDeleted
  canUseClubs
  contact {
    ...ContactInfo
  }
}

# User with full details (association + contact + addresses)
fragment FullUser on User {
  id
  firstName
  lastName
  email
  phone
  dob
  birthdayMonth
  birthdayDay
  dateJoined
  role
  isDeleted
  canUseClubs
  association {
    ...BasicAssociation
  }
  contact {
    ...ContactInfo
  }
  addresses {
    ...AddressInfo
  }
}
