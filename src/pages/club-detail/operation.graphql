query AdminClubDetailById($adminClubByIdId: ID!) {
  adminClubById(id: $adminClubByIdId) {
    ...AdminClubWithTemplate
  }
}

query ClubMembers(
  $clubTemplateId: ID!
  $paginationArgs: PaginationArgs
  $filter: ClubMembersFilterInput
  $orderBy: ClubMembersOrderInput
) {
  clubMembers(
    clubTemplateId: $clubTemplateId
    paginationArgs: $paginationArgs
    filter: $filter
    orderBy: $orderBy
  ) {
    items {
      ...ClubMembershipInfo
    }
    ...ClubMembersPaginationInfo
  }
}

query AdminClubPosts(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: AdminClubPostsFilterInput
) {
  adminClubPosts(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
    items {
      ...AdminClubPostWithProfile
    }
    ...AdminClubPostsPaginationInfo
  }
}

query AdminClubPostById($postId: ID!) {
  adminClubPostById(postId: $postId) {
    ...AdminClubPostWithProfile
  }
}

query AdminClubEvents(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: AdminClubEventsFilterInput
) {
  adminClubEvents(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
    items {
      ...AdminClubEventWithProfile
    }
    ...AdminClubEventsPaginationInfo
  }
}

query UsersCreateEvents(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: UsersCreateEventsFilterInput
) {
  usersCreateEvents(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
    items {
      ...DetailedUser
    }
    ...UsersCreateEventsPaginationInfo
  }
}

query AdminClubEventById($clubEventId: ID!) {
  adminClubEventById(clubEventId: $clubEventId) {
    ...AdminClubEventBasic
    clubProfile {
      ...AdminClubProfileWithUser
    }
  }
}

query ClubRequestById($clubRequestByIdId: ID!) {
  clubRequestById(id: $clubRequestByIdId) {
    ...ClubRequestInfo
  }
}

mutation AdminRemoveClubPostById($postId: ID!) {
  adminRemoveClubPostById(postId: $postId) {
    id
    clubId
    content
    isPinned
    reactionCount
    hasReacted
    createdAt
    updatedAt
  }
}

query AdminClubMembers(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: ClubMembersFilterInput
  $orderBy: ClubMembersOrderInput
) {
  adminClubMembers(
    clubId: $clubId
    paginationArgs: $paginationArgs
    filter: $filter
    orderBy: $orderBy
  ) {
    items {
      id
      clubProfile {
        ...AdminClubProfileWithUser
      }
      status
      joinedAt
      deletedAt
    }
    ...AdminClubMembersPaginationInfo
  }
}

query UsersCreatePosts(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: UsersCreatePostFilterInput
) {
  usersCreatePosts(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
    items {
      ...DetailedUser
    }
    ...UsersCreatePostsPaginationInfo
  }
}

mutation AdminUnflagReportsByPostId($postId: ID!) {
  adminUnflagReportsByPostId(postId: $postId) {
    clubId
    id
    updatedAt
    createdAt
  }
}

mutation AdminDeleteClubEvent($clubEventId: ID!) {
  adminDeleteClubEvent(clubEventId: $clubEventId) {
    id
    name
    createdAt
    updatedAt
  }
}

mutation AdminUnflagReportsByEventId($eventId: ID!) {
  adminUnflagReportsByEventId(eventId: $eventId) {
    id
    name
    createdAt
    updatedAt
  }
}

mutation RemoveClubMember($input: RemoveClubMemberInput!) {
  removeClubMember(input: $input) {
    clubTemplateId
    membershipId
  }
}
