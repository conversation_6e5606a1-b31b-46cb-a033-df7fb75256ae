import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/Tooltip';
import { InfoIcon } from 'lucide-react';

export const ImageTooltip = () => {
  return (
    <Tooltip>
      <TooltipTrigger>
        <InfoIcon className='w-4 h-4' />
      </TooltipTrigger>
      <TooltipContent
        align='center'
        side='right'
        className='max-w-[300px] bg-gray-500 text-xs text-white'
      >
        Upload a landscape image (4:3 aspect ratio) in JPG or PNG format. Recommended size: at least
        800×600px. Max file size: 5MB.
      </TooltipContent>
    </Tooltip>
  );
};
