query ClubTemplates(
  $paginationArgs: PaginationArgs
  $filter: ClubTemplatesFilterInput
  $orderBy: ClubOrderInput
) {
  clubTemplates(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      ...DetailedClubTemplate
    }
    ...ClubTemplatePaginationInfo
  }
}

query ClubTemplateById($clubTemplateByIdId: ID!) {
  clubTemplateById(id: $clubTemplateByIdId) {
    ...ClubTemplateWithImage
  }
}

query AdminClubById($adminClubByIdId: ID!) {
  adminClubById(id: $adminClubByIdId) {
    ...AdminClubWithTemplate
  }
}

query AdminClubs(
  $associationId: ID!
  $paginationArgs: PaginationArgs
  $filter: ClubFilterInput
  $orderBy: ClubOrderInput
) {
  adminClubs(
    associationId: $associationId
    paginationArgs: $paginationArgs
    filter: $filter
    orderBy: $orderBy
  ) {
    items {
      ...AdminClubWithTemplate
    }
    total
    page
    limit
  }
}

mutation CreateClubTemplate($input: CreateClubTemplateInput!) {
  createClubTemplate(input: $input) {
    ...DetailedClubTemplate
  }
}

mutation UpdateClubTemplate($input: UpdateClubTemplateInput!) {
  updateClubTemplate(input: $input) {
    ...ClubTemplateWithImage
  }
}

mutation DeleteClubTemplate($deleteClubTemplateId: ID!) {
  deleteClubTemplate(id: $deleteClubTemplateId)
}

mutation CreateUploadUrl($input: CreateUploadFileInput!) {
  createUploadUrl(input: $input) {
    ...UploadFileWithPresignedUrl
  }
}

# ClubRequests query with search support for Requests tab
query ClubRequests(
  $paginationArgs: PaginationArgs
  $filter: ClubRequestFilterInput
  $orderBy: ClubRequestOrderInput
) {
  clubRequests(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      ...ClubRequestInfo
      user {
        ...DetailedUser
      }
    }
    total
    page
    limit
  }
}

mutation UpdateClubRequest($input: UpdateClubRequestInput!) {
  updateClubRequest(input: $input) {
    id
    clubProfile {
      id
      displayName
    }
    clubName
    clubDescription
    category
    clubAbout
    status
    createdAt
  }
}

query UsersRequestedClubCreation(
  $paginationArgs: PaginationArgs
  $filter: UsersRequestedClubCreationFilterInput
) {
  usersRequestedClubCreation(paginationArgs: $paginationArgs, filter: $filter) {
    items {
      id
      firstName
      lastName
      email
      phone
      dob
      birthdayMonth
      birthdayDay
      dateJoined
      role
      isDeleted
      canUseClubs
    }
    total
    page
    limit
  }
}

mutation ApprovedClubRequestCreation($clubRequestId: ID!, $clubTemplateId: ID!) {
  approvedClubRequestCreation(clubRequestId: $clubRequestId, clubTemplateId: $clubTemplateId) {
    id
    clubName
    clubDescription
    category
    clubAbout
    status
    createdAt
  }
}

mutation RejectedClubRequestCreation($clubRequestId: ID!) {
  rejectedClubRequestCreation(clubRequestId: $clubRequestId) {
    id
    clubName
    clubDescription
    category
    clubAbout
    status
    createdAt
  }
}
