import { renderHook, act } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';
import { ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import useNotificationActions from '../useNotificationActions';
import {
  useAdminUnflagReportsByPostIdMutation,
  useAdminUnflagReportsByEventIdMutation,
  useRejectedClubRequestCreationMutation,
  AdminReportActionType,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';

// Mock the dependencies
jest.mock('@/generated/graphql', () => ({
  useAdminUnflagReportsByPostIdMutation: jest.fn(),
  useAdminUnflagReportsByEventIdMutation: jest.fn(),
  useAdminRemoveClubPostByIdMutation: jest.fn(),
  useAdminDeleteClubEventMutation: jest.fn(),
  useMarkAdminNotificationsAsReadMutation: jest.fn(),
  useRejectedClubRequestCreationMutation: jest.fn(),
  AdminReportActionType: {
    Unflag: 'UNFLAG',
    Declined: 'DECLINED',
    Delete: 'DELETE',
    Approved: 'APPROVED',
  },
}));

jest.mock('@/hooks/useToast', () => ({
  useToast: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

const mockToast = jest.fn();
const mockUnflagPostMutation = jest.fn();
const mockUnflagEventMutation = jest.fn();
const mockDeclineClubRequestMutation = jest.fn();

// Mock cache for testing
const mockCache = {
  modify: jest.fn(),
  readFragment: jest.fn(),
  writeFragment: jest.fn(),
};

const wrapper = ({ children }: { children: ReactNode }) => (
  <BrowserRouter>
    <MockedProvider mocks={[]} addTypename={false}>
      {children}
    </MockedProvider>
  </BrowserRouter>
);

describe('useNotificationActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    (useToast as jest.Mock).mockReturnValue({ toast: mockToast });
    (useAdminUnflagReportsByPostIdMutation as jest.Mock).mockReturnValue([mockUnflagPostMutation]);
    (useAdminUnflagReportsByEventIdMutation as jest.Mock).mockReturnValue([mockUnflagEventMutation]);
    (useRejectedClubRequestCreationMutation as jest.Mock).mockReturnValue([mockDeclineClubRequestMutation]);
  });

  describe('handleUnflagNotification', () => {
    it('should update cache with both action and isRead when unflagging a post', async () => {
      const mockRefetch = jest.fn();
      const { result } = renderHook(() => useNotificationActions({ refetch: mockRefetch }), {
        wrapper,
      });

      // Mock the mutation to call the update function
      mockUnflagPostMutation.mockImplementation(({ update }) => {
        update(mockCache);
        return Promise.resolve();
      });

      // Mock readFragment to return a notification
      mockCache.readFragment.mockReturnValue({
        id: 'notification-1',
        isRead: false,
        payload: { postId: 'post-123' },
      });

      // Mock existing cache data
      const mockExisting = {
        items: [{ __ref: 'AdminNotification:notification-1' }],
        total: 1,
      };

      mockCache.modify.mockImplementation(({ fields }) => {
        fields.adminNotifications(mockExisting);
      });

      await act(async () => {
        await result.current.handleUnflagNotification(true, 'post-123', 'notification-1');
      });

      // Verify cache.modify was called
      expect(mockCache.modify).toHaveBeenCalledWith({
        fields: {
          adminNotifications: expect.any(Function),
        },
      });

      // Verify writeFragment was called with correct data
      expect(mockCache.writeFragment).toHaveBeenCalledWith({
        id: 'AdminNotification:notification-1',
        fragment: expect.any(Object),
        data: {
          action: AdminReportActionType.Unflag,
          isRead: true,
        },
      });

      // Verify success toast
      expect(mockToast).toHaveBeenCalledWith({
        variant: 'success',
        title: 'Post unflagged successfully',
        duration: expect.any(Number),
      });
    });

    it('should update cache with both action and isRead when unflagging an event', async () => {
      const mockRefetch = jest.fn();
      const { result } = renderHook(() => useNotificationActions({ refetch: mockRefetch }), {
        wrapper,
      });

      // Mock the mutation to call the update function
      mockUnflagEventMutation.mockImplementation(({ update }) => {
        update(mockCache);
        return Promise.resolve();
      });

      // Mock readFragment to return a notification
      mockCache.readFragment.mockReturnValue({
        id: 'notification-2',
        isRead: false,
        payload: { eventId: 'event-456' },
      });

      // Mock existing cache data
      const mockExisting = {
        items: [{ __ref: 'AdminNotification:notification-2' }],
        total: 1,
      };

      mockCache.modify.mockImplementation(({ fields }) => {
        fields.adminNotifications(mockExisting);
      });

      await act(async () => {
        await result.current.handleUnflagNotification(false, 'event-456', 'notification-2');
      });

      // Verify writeFragment was called with correct data
      expect(mockCache.writeFragment).toHaveBeenCalledWith({
        id: 'AdminNotification:notification-2',
        fragment: expect.any(Object),
        data: {
          action: AdminReportActionType.Unflag,
          isRead: true,
        },
      });

      // Verify success toast
      expect(mockToast).toHaveBeenCalledWith({
        variant: 'success',
        title: 'Event unflagged successfully',
        duration: expect.any(Number),
      });
    });
  });

  describe('handleDeclineClubRequest', () => {
    it('should update cache with both action and isRead when declining a club request', async () => {
      const mockRefetch = jest.fn();
      const { result } = renderHook(() => useNotificationActions({ refetch: mockRefetch }), {
        wrapper,
      });

      // Mock the mutation to call the update function
      mockDeclineClubRequestMutation.mockImplementation(({ update }) => {
        update(mockCache);
        return Promise.resolve();
      });

      // Mock readFragment to return a notification
      mockCache.readFragment.mockReturnValue({
        id: 'notification-3',
        isRead: false,
        payload: { clubRequestId: 'request-789' },
        clubRequest: { id: 'request-789' },
      });

      // Mock existing cache data
      const mockExisting = {
        items: [{ __ref: 'AdminNotification:notification-3' }],
        total: 1,
      };

      mockCache.modify.mockImplementation(({ fields }) => {
        fields.adminNotifications(mockExisting);
      });

      await act(async () => {
        await result.current.handleDeclineClubRequest('request-789', 'notification-3');
      });

      // Verify writeFragment was called with correct data
      expect(mockCache.writeFragment).toHaveBeenCalledWith({
        id: 'AdminNotification:notification-3',
        fragment: expect.any(Object),
        data: {
          action: AdminReportActionType.Declined,
          isRead: true,
        },
      });

      // Verify success toast
      expect(mockToast).toHaveBeenCalledWith({
        variant: 'success',
        title: 'Club request declined successfully',
        duration: expect.any(Number),
      });
    });
  });

  describe('loading states', () => {
    it('should track loading states correctly', async () => {
      const { result } = renderHook(() => useNotificationActions({}), { wrapper });

      // Initially no loading states
      expect(result.current.getItemLoadingState('test-id')).toEqual({
        isUnflagging: false,
        isRemoving: false,
        isDecliningClubRequest: false,
        isMarkingAsRead: false,
      });

      // Mock a slow mutation
      mockUnflagPostMutation.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      // Start unflagging
      act(() => {
        result.current.handleUnflagNotification(true, 'post-123', 'test-id');
      });

      // Should be in loading state
      expect(result.current.getItemLoadingState('test-id').isUnflagging).toBe(true);
    });
  });
});
