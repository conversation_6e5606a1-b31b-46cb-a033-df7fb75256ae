# Cache Update Demonstration

## How the Fix Works

### Before the Fix

When a user unflagged a notification:

1. **Server Response**: `{ action: "UNFLAG", isRead: true }`
2. **Cache Update**: Only `action: "UNFLAG"` was updated
3. **UI State**: Item remained in "unread" section
4. **Pagination Issue**: Item appeared again in "seen" section when loading more data

```typescript
// OLD CACHE UPDATE (BROKEN)
cache.writeFragment({
  id: itemRef.__ref,
  fragment: gql`
    fragment UpdateNotificationAction on AdminNotification {
      action  // Only updating action field
    }
  `,
  data: {
    action: AdminReportActionType.Unflag,
    // Missing: isRead: true
  },
});
```

### After the Fix

When a user unflaggs a notification:

1. **Server Response**: `{ action: "UNFLAG", isRead: true }`
2. **Cache Update**: Both `action: "UNFLAG"` and `isRead: true` are updated
3. **UI State**: Item immediately moves to "seen" section
4. **Pagination**: No duplicates, consistent state

```typescript
// NEW CACHE UPDATE (FIXED)
cache.writeFragment({
  id: itemRef.__ref,
  fragment: gql`
    fragment UpdateNotificationActionAndRead on AdminNotification {
      action
      isRead  // Now updating both fields
    }
  `,
  data: {
    action: AdminReportActionType.Unflag,
    isRead: true, // Server marks as read when unflagged
  },
});
```

## Visual Flow

### Unflagging a Post Notification

```
BEFORE FIX:
[Unread Section]
├── Post Report #1 (isRead: false) 
├── Post Report #2 (isRead: false) ← User clicks "Unflag"
└── Post Report #3 (isRead: false)

[User clicks unflag on Post Report #2]
↓ Server processes unflag
↓ Server sets: action="UNFLAG", isRead=true
↓ Cache only updates: action="UNFLAG"

[Unread Section] - WRONG!
├── Post Report #1 (isRead: false)
├── Post Report #2 (isRead: false, action: "UNFLAG") ← Still here!
└── Post Report #3 (isRead: false)

[User scrolls to load more]
↓ Pagination loads more data
↓ Server returns Post Report #2 with isRead=true

[Seen Section] - DUPLICATE!
└── Post Report #2 (isRead: true, action: "UNFLAG") ← Appears here too!
```

```
AFTER FIX:
[Unread Section]
├── Post Report #1 (isRead: false)
├── Post Report #2 (isRead: false) ← User clicks "Unflag"
└── Post Report #3 (isRead: false)

[User clicks unflag on Post Report #2]
↓ Server processes unflag
↓ Server sets: action="UNFLAG", isRead=true
↓ Cache updates: action="UNFLAG", isRead=true

[Unread Section] - CORRECT!
├── Post Report #1 (isRead: false)
└── Post Report #3 (isRead: false)

[Seen Section] - CORRECT!
└── Post Report #2 (isRead: true, action: "UNFLAG") ← Moved here immediately!

[User scrolls to load more]
↓ Pagination loads more data
↓ No duplicates, consistent state maintained
```

## Cache Merge Strategy

The enhanced cache merge policy ensures unique items:

```typescript
// Enhanced merge policy prevents duplicates
adminNotifications: {
  keyArgs: false,
  merge(existing: any, incoming: any) {
    if (!existing) return incoming;

    const existingItems = existing.items || [];
    const incomingItems = incoming.items || [];

    // Create unique set - incoming items replace existing ones by ID
    const existingItemsMap = new Map();
    existingItems.forEach((item: any) => {
      const id = item.__ref?.split(':')[1] || item.id;
      if (id) {
        existingItemsMap.set(id, item);
      }
    });

    // Replace with incoming items (updated state)
    incomingItems.forEach((item: any) => {
      const id = item.__ref?.split(':')[1] || item.id;
      if (id) {
        existingItemsMap.set(id, item); // Replaces existing
      }
    });

    const mergedItems = Array.from(existingItemsMap.values());

    return {
      ...incoming,
      items: mergedItems,
      total: incoming.total,
    };
  },
}
```

## Testing the Fix

You can verify the fix works by:

1. **Load notifications** with some unread items
2. **Unflag a post/event** or **decline a club request**
3. **Observe immediate UI update** - item moves to "seen" section
4. **Scroll to load more** - no duplicates appear
5. **Check cache state** - both `action` and `isRead` are updated

## Key Benefits

✅ **Immediate UI feedback** - no waiting for refetch  
✅ **No duplicate items** - consistent pagination  
✅ **Cache-server sync** - cache matches server state  
✅ **Better UX** - smooth, predictable behavior  
✅ **Unique set behavior** - incoming items replace existing ones by ID
